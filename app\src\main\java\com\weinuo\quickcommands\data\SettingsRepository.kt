package com.weinuo.quickcommands.data

import android.content.Context
import android.content.SharedPreferences
import android.util.Log

import com.weinuo.quickcommands.model.GlobalSettings
import com.weinuo.quickcommands.model.AppImportance
import com.weinuo.quickcommands.model.CleanupStrategy
import com.weinuo.quickcommands.model.CleanupRule
import com.weinuo.quickcommands.model.CleanupRuleType
import com.weinuo.quickcommands.model.NetworkState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 设置数据仓库，负责管理全局设置和应用单独设置
 */
class SettingsRepository(context: Context) {
    private val sharedPreferences: SharedPreferences = context.getSharedPreferences(
        PREFS_NAME, Context.MODE_PRIVATE
    )

    private val _globalSettings = MutableStateFlow(loadGlobalSettings())
    val globalSettings: StateFlow<GlobalSettings> = _globalSettings.asStateFlow()



    /**
     * 加载全局设置（仅保留快捷指令需要的设置）
     */
    private fun loadGlobalSettings(): GlobalSettings {
        val experimentalFeaturesEnabled = sharedPreferences.getBoolean(KEY_GLOBAL_EXPERIMENTAL_FEATURES_ENABLED, false)
        val experimentalFeaturesUnlocked = sharedPreferences.getBoolean(KEY_GLOBAL_EXPERIMENTAL_FEATURES_UNLOCKED, false)

        Log.d(TAG, "Loading global settings: experimentalFeaturesEnabled=$experimentalFeaturesEnabled, experimentalFeaturesUnlocked=$experimentalFeaturesUnlocked")

        return GlobalSettings(
            widgetUpdateEnabled = sharedPreferences.getBoolean(KEY_GLOBAL_WIDGET_UPDATE_ENABLED, false),
            widgetUpdateInterval = sharedPreferences.getInt(KEY_GLOBAL_WIDGET_UPDATE_INTERVAL, 24),
            savedNetworkState = loadSavedNetworkState(),
            experimentalFeaturesEnabled = experimentalFeaturesEnabled,
            experimentalFeaturesUnlocked = experimentalFeaturesUnlocked,
            appLanguage = sharedPreferences.getString(KEY_GLOBAL_APP_LANGUAGE, "system") ?: "system",
            searchFieldPlaceholderFontWeight = sharedPreferences.getString(KEY_GLOBAL_SEARCH_FIELD_PLACEHOLDER_FONT_WEIGHT, "medium") ?: "medium",
            searchFieldIconWeight = sharedPreferences.getString(KEY_GLOBAL_SEARCH_FIELD_ICON_WEIGHT, "medium") ?: "medium",
            topAppBarHeight = sharedPreferences.getInt(KEY_GLOBAL_TOP_APP_BAR_HEIGHT, 55),
            topAppBarTitleFontSize = sharedPreferences.getInt(KEY_GLOBAL_TOP_APP_BAR_TITLE_FONT_SIZE, 25),
            topAppBarTitleFontWeight = sharedPreferences.getString(KEY_GLOBAL_TOP_APP_BAR_TITLE_FONT_WEIGHT, "bold") ?: "bold",
            topAppBarTitleVerticalOffset = sharedPreferences.getInt(KEY_GLOBAL_TOP_APP_BAR_TITLE_VERTICAL_OFFSET, 0),
            topAppBarTitleHorizontalOffset = sharedPreferences.getInt(KEY_GLOBAL_TOP_APP_BAR_TITLE_HORIZONTAL_OFFSET, 0),
            topAppBarType = sharedPreferences.getString(KEY_GLOBAL_TOP_APP_BAR_TYPE, "standard") ?: "standard",
            screenTitleFontSize = sharedPreferences.getInt(KEY_GLOBAL_SCREEN_TITLE_FONT_SIZE, 20),
            formSectionTitleFontSize = sharedPreferences.getInt(KEY_GLOBAL_FORM_SECTION_TITLE_FONT_SIZE, 17),

            // 卡片样式设置（天空蓝主题专用）- 使用当前代码中的默认值
            cardCornerRadius = sharedPreferences.getInt(KEY_GLOBAL_CARD_CORNER_RADIUS, 20),
            cardDefaultHorizontalPadding = sharedPreferences.getInt(KEY_GLOBAL_CARD_DEFAULT_HORIZONTAL_PADDING, 13),
            cardDefaultVerticalPadding = sharedPreferences.getInt(KEY_GLOBAL_CARD_DEFAULT_VERTICAL_PADDING, 14),
            cardSettingsVerticalPadding = sharedPreferences.getInt(KEY_GLOBAL_CARD_SETTINGS_VERTICAL_PADDING, 6),
            cardCompactHorizontalPadding = sharedPreferences.getInt(KEY_GLOBAL_CARD_COMPACT_HORIZONTAL_PADDING, 11),
            cardCompactVerticalPadding = sharedPreferences.getInt(KEY_GLOBAL_CARD_COMPACT_VERTICAL_PADDING, 4),
            cardLargeHorizontalPadding = sharedPreferences.getInt(KEY_GLOBAL_CARD_LARGE_HORIZONTAL_PADDING, 19),
            cardLargeVerticalPadding = sharedPreferences.getInt(KEY_GLOBAL_CARD_LARGE_VERTICAL_PADDING, 12),
            cardItemSpacing = sharedPreferences.getInt(KEY_GLOBAL_CARD_ITEM_SPACING, 8),
            cardSectionSpacing = sharedPreferences.getInt(KEY_GLOBAL_CARD_SECTION_SPACING, 20),
            cardContentVerticalSpacing = sharedPreferences.getInt(KEY_GLOBAL_CARD_CONTENT_VERTICAL_SPACING, 0),
            cardContentHorizontalSpacing = sharedPreferences.getInt(KEY_GLOBAL_CARD_CONTENT_HORIZONTAL_SPACING, 12),
            cardSelectedElevation = sharedPreferences.getInt(KEY_GLOBAL_CARD_SELECTED_ELEVATION, 0),
            cardSelectedBorderWidth = sharedPreferences.getInt(KEY_GLOBAL_CARD_SELECTED_BORDER_WIDTH, 3),
            cardTitleFontSize = sharedPreferences.getInt(KEY_GLOBAL_CARD_TITLE_FONT_SIZE, 15),
            cardTitleFontWeight = sharedPreferences.getString(KEY_GLOBAL_CARD_TITLE_FONT_WEIGHT, "medium") ?: "medium",
            cardContentFontSize = sharedPreferences.getInt(KEY_GLOBAL_CARD_CONTENT_FONT_SIZE, 13),
            cardContentFontWeight = sharedPreferences.getString(KEY_GLOBAL_CARD_CONTENT_FONT_WEIGHT, "medium") ?: "medium",
            cardIconSize = sharedPreferences.getInt(KEY_GLOBAL_CARD_ICON_SIZE, 48),

            // 页面布局设置（天空蓝主题专用）- 使用当前代码中的默认值
            pageContentHorizontalPadding = sharedPreferences.getInt(KEY_GLOBAL_PAGE_CONTENT_HORIZONTAL_PADDING, 16),
            pageSearchFieldMargin = sharedPreferences.getInt(KEY_GLOBAL_PAGE_SEARCH_FIELD_MARGIN, 16),
            pageHeaderSpacing = sharedPreferences.getInt(KEY_GLOBAL_PAGE_HEADER_SPACING, 16),
            pageBottomPadding = sharedPreferences.getInt(KEY_GLOBAL_PAGE_BOTTOM_PADDING, 88),
            pageScrollContentSpacing = sharedPreferences.getInt(KEY_GLOBAL_PAGE_SCROLL_CONTENT_SPACING, 8),

            // UI间距设置（天空蓝主题专用）
            uiSettingsItemVerticalPadding = sharedPreferences.getInt(KEY_UI_SETTINGS_ITEM_VERTICAL_PADDING, 12),
            uiDividerHorizontalPadding = sharedPreferences.getInt(KEY_UI_DIVIDER_HORIZONTAL_PADDING, 0),
            uiSettingsCardPadding = sharedPreferences.getInt(KEY_UI_SETTINGS_CARD_PADDING, 16),
            uiSettingsItemSpacing = sharedPreferences.getInt(KEY_UI_SETTINGS_ITEM_SPACING, 16),
            uiSettingsTitleSpacing = sharedPreferences.getInt(KEY_UI_SETTINGS_TITLE_SPACING, 8),
            uiSettingsDescriptionSpacing = sharedPreferences.getInt(KEY_UI_SETTINGS_DESCRIPTION_SPACING, 4),
            uiSettingsGroupTitleHorizontalPadding = sharedPreferences.getInt(KEY_UI_SETTINGS_GROUP_TITLE_HORIZONTAL_PADDING, 13),
            uiSettingsGroupTitleTopPadding = sharedPreferences.getInt(KEY_UI_SETTINGS_GROUP_TITLE_TOP_PADDING, 30),
            uiSettingsGroupTitleBottomPadding = sharedPreferences.getInt(KEY_UI_SETTINGS_GROUP_TITLE_BOTTOM_PADDING, 6),
            uiDividerVisible = sharedPreferences.getBoolean(KEY_UI_DIVIDER_VISIBLE, true),

            // 用户体验设置
            checkupResultDelayEnabled = sharedPreferences.getBoolean(KEY_CHECKUP_RESULT_DELAY_ENABLED, false),
            checkupDisplayDurationSeconds = sharedPreferences.getInt(KEY_CHECKUP_DISPLAY_DURATION_SECONDS, 5),
            fixedCheckupScoreEnabled = sharedPreferences.getBoolean(KEY_FIXED_CHECKUP_SCORE_ENABLED, false),
            fixedCheckupScore = sharedPreferences.getInt(KEY_FIXED_CHECKUP_SCORE, 100),

            // 对话框间距设置（天空蓝主题专用）
            dialogOuterPadding = sharedPreferences.getInt(KEY_DIALOG_OUTER_PADDING, 12),
            dialogIconBottomPadding = sharedPreferences.getInt(KEY_DIALOG_ICON_BOTTOM_PADDING, 16),
            dialogTitleBottomPadding = sharedPreferences.getInt(KEY_DIALOG_TITLE_BOTTOM_PADDING, 16),
            dialogContentBottomPadding = sharedPreferences.getInt(KEY_DIALOG_CONTENT_BOTTOM_PADDING, 4),
            dialogContentVerticalPadding = sharedPreferences.getInt(KEY_DIALOG_CONTENT_VERTICAL_PADDING, 8),
            dialogInputBottomPadding = sharedPreferences.getInt(KEY_DIALOG_INPUT_BOTTOM_PADDING, 8),
            dialogButtonTopPadding = sharedPreferences.getInt(KEY_DIALOG_BUTTON_TOP_PADDING, 2),
            dialogButtonBottomPadding = sharedPreferences.getInt(KEY_DIALOG_BUTTON_BOTTOM_PADDING, 14),
            dialogTitleFontSize = sharedPreferences.getInt(KEY_DIALOG_TITLE_FONT_SIZE, 17),
            dialogDividerHorizontalPadding = sharedPreferences.getInt(KEY_DIALOG_DIVIDER_HORIZONTAL_PADDING, 12),

            // 天空蓝主题颜色配置 - 使用当前代码中的默认颜色值
            // 主要颜色系统
            skyBluePrimary = sharedPreferences.getString(KEY_SKY_BLUE_PRIMARY, "0xFF0A59F7") ?: "0xFF0A59F7",
            skyBlueOnPrimary = sharedPreferences.getString(KEY_SKY_BLUE_ON_PRIMARY, "0xFFFFFFFF") ?: "0xFFFFFFFF",
            skyBluePrimaryContainer = sharedPreferences.getString(KEY_SKY_BLUE_PRIMARY_CONTAINER, "0x330A59F7") ?: "0x330A59F7",
            skyBlueOnPrimaryContainer = sharedPreferences.getString(KEY_SKY_BLUE_ON_PRIMARY_CONTAINER, "0xE5000000") ?: "0xE5000000",

            // 次要颜色系统
            skyBlueSecondary = sharedPreferences.getString(KEY_SKY_BLUE_SECONDARY, "0x99000000") ?: "0x99000000",
            skyBlueOnSecondary = sharedPreferences.getString(KEY_SKY_BLUE_ON_SECONDARY, "0x99FFFFFF") ?: "0x99FFFFFF",
            skyBlueSecondaryContainer = sharedPreferences.getString(KEY_SKY_BLUE_SECONDARY_CONTAINER, "0xFFF1F3F5") ?: "0xFFF1F3F5",
            skyBlueOnSecondaryContainer = sharedPreferences.getString(KEY_SKY_BLUE_ON_SECONDARY_CONTAINER, "0x99000000") ?: "0x99000000",

            // 第三颜色系统
            skyBlueTertiary = sharedPreferences.getString(KEY_SKY_BLUE_TERTIARY, "0x66000000") ?: "0x66000000",
            skyBlueOnTertiary = sharedPreferences.getString(KEY_SKY_BLUE_ON_TERTIARY, "0x66FFFFFF") ?: "0x66FFFFFF",
            skyBlueTertiaryContainer = sharedPreferences.getString(KEY_SKY_BLUE_TERTIARY_CONTAINER, "0xFFE5E5EA") ?: "0xFFE5E5EA",
            skyBlueOnTertiaryContainer = sharedPreferences.getString(KEY_SKY_BLUE_ON_TERTIARY_CONTAINER, "0x66000000") ?: "0x66000000",

            // 错误颜色系统
            skyBlueError = sharedPreferences.getString(KEY_SKY_BLUE_ERROR, "0xFFE84026") ?: "0xFFE84026",
            skyBlueOnError = sharedPreferences.getString(KEY_SKY_BLUE_ON_ERROR, "0xFFFFFFFF") ?: "0xFFFFFFFF",
            skyBlueErrorContainer = sharedPreferences.getString(KEY_SKY_BLUE_ERROR_CONTAINER, "0xFFED6F21") ?: "0xFFED6F21",
            skyBlueOnErrorContainer = sharedPreferences.getString(KEY_SKY_BLUE_ON_ERROR_CONTAINER, "0xFFFFFFFF") ?: "0xFFFFFFFF",

            // 表面颜色系统
            skyBlueBackground = sharedPreferences.getString(KEY_SKY_BLUE_BACKGROUND, "0xFFF1F3F5") ?: "0xFFF1F3F5",
            skyBlueOnBackground = sharedPreferences.getString(KEY_SKY_BLUE_ON_BACKGROUND, "0xE5000000") ?: "0xE5000000",
            skyBlueSurface = sharedPreferences.getString(KEY_SKY_BLUE_SURFACE, "0xFFF1F3F5") ?: "0xFFF1F3F5",
            skyBlueOnSurface = sharedPreferences.getString(KEY_SKY_BLUE_ON_SURFACE, "0xE5000000") ?: "0xE5000000",
            skyBlueSurfaceVariant = sharedPreferences.getString(KEY_SKY_BLUE_SURFACE_VARIANT, "0xFFF1F3F5") ?: "0xFFF1F3F5",
            skyBlueOnSurfaceVariant = sharedPreferences.getString(KEY_SKY_BLUE_ON_SURFACE_VARIANT, "0x99000000") ?: "0x99000000",

            // 扩展颜色
            skyBlueConfirm = sharedPreferences.getString(KEY_SKY_BLUE_CONFIRM, "0xFF64BB5C") ?: "0xFF64BB5C",
            skyBlueFontEmphasize = sharedPreferences.getString(KEY_SKY_BLUE_FONT_EMPHASIZE, "0xFF0A59F7") ?: "0xFF0A59F7",
            skyBlueIconEmphasize = sharedPreferences.getString(KEY_SKY_BLUE_ICON_EMPHASIZE, "0xFF0A59F7") ?: "0xFF0A59F7",
            skyBlueIconSubEmphasize = sharedPreferences.getString(KEY_SKY_BLUE_ICON_SUB_EMPHASIZE, "0x660A59F7") ?: "0x660A59F7",
            skyBlueBackgroundEmphasize = sharedPreferences.getString(KEY_SKY_BLUE_BACKGROUND_EMPHASIZE, "0xFF0A59F7") ?: "0xFF0A59F7",
            skyBlueBackgroundFocus = sharedPreferences.getString(KEY_SKY_BLUE_BACKGROUND_FOCUS, "0xFFF1F3F5") ?: "0xFFF1F3F5",

            // 底部导航栏颜色配置
            skyBlueBottomNavBackground = sharedPreferences.getString(KEY_SKY_BLUE_BOTTOM_NAV_BACKGROUND, "0xFFF1F3F5") ?: "0xFFF1F3F5",
            skyBlueBottomNavSelectedIcon = sharedPreferences.getString(KEY_SKY_BLUE_BOTTOM_NAV_SELECTED_ICON, "0xFF0A59F7") ?: "0xFF0A59F7",
            skyBlueBottomNavUnselectedIcon = sharedPreferences.getString(KEY_SKY_BLUE_BOTTOM_NAV_UNSELECTED_ICON, "0x99000000") ?: "0x99000000",

            // 标题栏颜色配置
            skyBlueTopBarBackground = sharedPreferences.getString(KEY_SKY_BLUE_TOP_BAR_BACKGROUND, "0xFFF1F3F5") ?: "0xFFF1F3F5",

            // 底部导航栏尺寸配置（天空蓝主题专用）- 使用当前代码中的默认值
            bottomNavHeight = sharedPreferences.getInt(KEY_GLOBAL_BOTTOM_NAV_HEIGHT, 80),
            bottomNavHorizontalPadding = sharedPreferences.getInt(KEY_GLOBAL_BOTTOM_NAV_HORIZONTAL_PADDING, 16),
            bottomNavVerticalPadding = sharedPreferences.getInt(KEY_GLOBAL_BOTTOM_NAV_VERTICAL_PADDING, 7),
            bottomNavItemCornerRadius = sharedPreferences.getInt(KEY_GLOBAL_BOTTOM_NAV_ITEM_CORNER_RADIUS, 16),
            bottomNavItemOuterPadding = sharedPreferences.getInt(KEY_GLOBAL_BOTTOM_NAV_ITEM_OUTER_PADDING, 4),
            bottomNavItemVerticalPadding = sharedPreferences.getInt(KEY_GLOBAL_BOTTOM_NAV_ITEM_VERTICAL_PADDING, 8),
            bottomNavItemHorizontalPadding = sharedPreferences.getInt(KEY_GLOBAL_BOTTOM_NAV_ITEM_HORIZONTAL_PADDING, 12),
            bottomNavIconSize = sharedPreferences.getInt(KEY_GLOBAL_BOTTOM_NAV_ICON_SIZE, 24),
            bottomNavIconTextSpacing = sharedPreferences.getInt(KEY_GLOBAL_BOTTOM_NAV_ICON_TEXT_SPACING, 4),
            bottomNavTextFontSize = sharedPreferences.getInt(KEY_GLOBAL_BOTTOM_NAV_TEXT_FONT_SIZE, 11),
            bottomNavSelectedFontWeight = sharedPreferences.getString(KEY_GLOBAL_BOTTOM_NAV_SELECTED_FONT_WEIGHT, "medium") ?: "medium",
            bottomNavUnselectedFontWeight = sharedPreferences.getString(KEY_GLOBAL_BOTTOM_NAV_UNSELECTED_FONT_WEIGHT, "normal") ?: "normal",
            bottomNavColorAnimationDuration = sharedPreferences.getInt(KEY_GLOBAL_BOTTOM_NAV_COLOR_ANIMATION_DURATION, 150),
            bottomNavBackgroundAnimationDuration = sharedPreferences.getInt(KEY_GLOBAL_BOTTOM_NAV_BACKGROUND_ANIMATION_DURATION, 150),
            bottomNavItemArrangement = sharedPreferences.getString(KEY_GLOBAL_BOTTOM_NAV_ITEM_ARRANGEMENT, "spaceEvenly") ?: "spaceEvenly",

            // 对话框样式设置（天空蓝主题专用）
            dialogBlurEnabled = sharedPreferences.getBoolean(KEY_GLOBAL_DIALOG_BLUR_ENABLED, true),
            dialogCornerRadius = sharedPreferences.getInt(KEY_GLOBAL_DIALOG_CORNER_RADIUS, 28),
            dialogBlurIntensity = sharedPreferences.getFloat(KEY_GLOBAL_DIALOG_BLUR_INTENSITY, 0.6f),

            // 顶部应用栏按钮样式设置（天空蓝主题专用）
            topAppBarButtonCircleBackgroundEnabled = sharedPreferences.getBoolean(KEY_GLOBAL_TOP_APP_BAR_BUTTON_CIRCLE_BACKGROUND_ENABLED, true),
            topAppBarButtonCircleBackgroundSize = sharedPreferences.getInt(KEY_GLOBAL_TOP_APP_BAR_BUTTON_CIRCLE_BACKGROUND_SIZE, 40),
            topAppBarButtonCircleBackgroundHorizontalMargin = sharedPreferences.getInt(KEY_GLOBAL_TOP_APP_BAR_BUTTON_CIRCLE_BACKGROUND_HORIZONTAL_MARGIN, 28),
            topAppBarButtonCircleBackgroundRightMargin = sharedPreferences.getInt(KEY_GLOBAL_TOP_APP_BAR_BUTTON_CIRCLE_BACKGROUND_RIGHT_MARGIN, 3),

            // 导航项显示控制设置（天空蓝主题专用）
            navigationItemsVisibility = loadNavigationItemsVisibility(),

            // 水球高级材质设置（天空蓝主题专用）
            waterBallAdvancedMaterialEnabled = sharedPreferences.getBoolean(KEY_GLOBAL_WATER_BALL_ADVANCED_MATERIAL_ENABLED, false),
            waterWaveReserveSpaceEnabled = sharedPreferences.getBoolean(KEY_GLOBAL_WATER_WAVE_RESERVE_SPACE_ENABLED, false),
            waterWaveStopThreshold = sharedPreferences.getInt(KEY_GLOBAL_WATER_WAVE_STOP_THRESHOLD, 90),

            // 悬浮加速球设置
            floatingAcceleratorEnabled = sharedPreferences.getBoolean(KEY_GLOBAL_FLOATING_ACCELERATOR_ENABLED, false)
        )
    }

    /**
     * 保存全局设置（仅保留快捷指令需要的设置）
     */
    fun saveGlobalSettings(settings: GlobalSettings) {
        sharedPreferences.edit().apply {
            putBoolean(KEY_GLOBAL_WIDGET_UPDATE_ENABLED, settings.widgetUpdateEnabled)
            putInt(KEY_GLOBAL_WIDGET_UPDATE_INTERVAL, settings.widgetUpdateInterval)
            putBoolean(KEY_GLOBAL_EXPERIMENTAL_FEATURES_ENABLED, settings.experimentalFeaturesEnabled)
            putBoolean(KEY_GLOBAL_EXPERIMENTAL_FEATURES_UNLOCKED, settings.experimentalFeaturesUnlocked)
            putString(KEY_GLOBAL_APP_LANGUAGE, settings.appLanguage)
            putString(KEY_GLOBAL_SEARCH_FIELD_PLACEHOLDER_FONT_WEIGHT, settings.searchFieldPlaceholderFontWeight)
            putString(KEY_GLOBAL_SEARCH_FIELD_ICON_WEIGHT, settings.searchFieldIconWeight)
            putInt(KEY_GLOBAL_TOP_APP_BAR_HEIGHT, settings.topAppBarHeight)
            putInt(KEY_GLOBAL_TOP_APP_BAR_TITLE_FONT_SIZE, settings.topAppBarTitleFontSize)
            putString(KEY_GLOBAL_TOP_APP_BAR_TITLE_FONT_WEIGHT, settings.topAppBarTitleFontWeight)
            putInt(KEY_GLOBAL_TOP_APP_BAR_TITLE_VERTICAL_OFFSET, settings.topAppBarTitleVerticalOffset)
            putInt(KEY_GLOBAL_TOP_APP_BAR_TITLE_HORIZONTAL_OFFSET, settings.topAppBarTitleHorizontalOffset)
            putString(KEY_GLOBAL_TOP_APP_BAR_TYPE, settings.topAppBarType)
            putInt(KEY_GLOBAL_SCREEN_TITLE_FONT_SIZE, settings.screenTitleFontSize)
            putInt(KEY_GLOBAL_FORM_SECTION_TITLE_FONT_SIZE, settings.formSectionTitleFontSize)

            // 卡片样式设置（天空蓝主题专用）
            putInt(KEY_GLOBAL_CARD_CORNER_RADIUS, settings.cardCornerRadius)
            putInt(KEY_GLOBAL_CARD_DEFAULT_HORIZONTAL_PADDING, settings.cardDefaultHorizontalPadding)
            putInt(KEY_GLOBAL_CARD_DEFAULT_VERTICAL_PADDING, settings.cardDefaultVerticalPadding)
            putInt(KEY_GLOBAL_CARD_SETTINGS_VERTICAL_PADDING, settings.cardSettingsVerticalPadding)
            putInt(KEY_GLOBAL_CARD_COMPACT_HORIZONTAL_PADDING, settings.cardCompactHorizontalPadding)
            putInt(KEY_GLOBAL_CARD_COMPACT_VERTICAL_PADDING, settings.cardCompactVerticalPadding)
            putInt(KEY_GLOBAL_CARD_LARGE_HORIZONTAL_PADDING, settings.cardLargeHorizontalPadding)
            putInt(KEY_GLOBAL_CARD_LARGE_VERTICAL_PADDING, settings.cardLargeVerticalPadding)
            putInt(KEY_GLOBAL_CARD_ITEM_SPACING, settings.cardItemSpacing)
            putInt(KEY_GLOBAL_CARD_SECTION_SPACING, settings.cardSectionSpacing)
            putInt(KEY_GLOBAL_CARD_CONTENT_VERTICAL_SPACING, settings.cardContentVerticalSpacing)
            putInt(KEY_GLOBAL_CARD_CONTENT_HORIZONTAL_SPACING, settings.cardContentHorizontalSpacing)
            putInt(KEY_GLOBAL_CARD_SELECTED_ELEVATION, settings.cardSelectedElevation)
            putInt(KEY_GLOBAL_CARD_SELECTED_BORDER_WIDTH, settings.cardSelectedBorderWidth)
            putInt(KEY_GLOBAL_CARD_TITLE_FONT_SIZE, settings.cardTitleFontSize)
            putString(KEY_GLOBAL_CARD_TITLE_FONT_WEIGHT, settings.cardTitleFontWeight)
            putInt(KEY_GLOBAL_CARD_CONTENT_FONT_SIZE, settings.cardContentFontSize)
            putString(KEY_GLOBAL_CARD_CONTENT_FONT_WEIGHT, settings.cardContentFontWeight)
            putInt(KEY_GLOBAL_CARD_ICON_SIZE, settings.cardIconSize)

            // 页面布局设置（天空蓝主题专用）
            putInt(KEY_GLOBAL_PAGE_CONTENT_HORIZONTAL_PADDING, settings.pageContentHorizontalPadding)
            putInt(KEY_GLOBAL_PAGE_SEARCH_FIELD_MARGIN, settings.pageSearchFieldMargin)
            putInt(KEY_GLOBAL_PAGE_HEADER_SPACING, settings.pageHeaderSpacing)
            putInt(KEY_GLOBAL_PAGE_BOTTOM_PADDING, settings.pageBottomPadding)
            putInt(KEY_GLOBAL_PAGE_SCROLL_CONTENT_SPACING, settings.pageScrollContentSpacing)

            // UI间距设置（天空蓝主题专用）
            putInt(KEY_UI_SETTINGS_ITEM_VERTICAL_PADDING, settings.uiSettingsItemVerticalPadding)
            putInt(KEY_UI_DIVIDER_HORIZONTAL_PADDING, settings.uiDividerHorizontalPadding)
            putInt(KEY_UI_SETTINGS_CARD_PADDING, settings.uiSettingsCardPadding)
            putInt(KEY_UI_SETTINGS_ITEM_SPACING, settings.uiSettingsItemSpacing)
            putInt(KEY_UI_SETTINGS_TITLE_SPACING, settings.uiSettingsTitleSpacing)
            putInt(KEY_UI_SETTINGS_DESCRIPTION_SPACING, settings.uiSettingsDescriptionSpacing)
            putInt(KEY_UI_SETTINGS_GROUP_TITLE_HORIZONTAL_PADDING, settings.uiSettingsGroupTitleHorizontalPadding)
            putInt(KEY_UI_SETTINGS_GROUP_TITLE_TOP_PADDING, settings.uiSettingsGroupTitleTopPadding)
            putInt(KEY_UI_SETTINGS_GROUP_TITLE_BOTTOM_PADDING, settings.uiSettingsGroupTitleBottomPadding)
            putBoolean(KEY_UI_DIVIDER_VISIBLE, settings.uiDividerVisible)

            // 用户体验设置
            putBoolean(KEY_CHECKUP_RESULT_DELAY_ENABLED, settings.checkupResultDelayEnabled)
            putInt(KEY_CHECKUP_DISPLAY_DURATION_SECONDS, settings.checkupDisplayDurationSeconds)
            putBoolean(KEY_FIXED_CHECKUP_SCORE_ENABLED, settings.fixedCheckupScoreEnabled)
            putInt(KEY_FIXED_CHECKUP_SCORE, settings.fixedCheckupScore)

            // 对话框间距设置（天空蓝主题专用）
            putInt(KEY_DIALOG_OUTER_PADDING, settings.dialogOuterPadding)
            putInt(KEY_DIALOG_ICON_BOTTOM_PADDING, settings.dialogIconBottomPadding)
            putInt(KEY_DIALOG_TITLE_BOTTOM_PADDING, settings.dialogTitleBottomPadding)
            putInt(KEY_DIALOG_CONTENT_BOTTOM_PADDING, settings.dialogContentBottomPadding)
            putInt(KEY_DIALOG_CONTENT_VERTICAL_PADDING, settings.dialogContentVerticalPadding)
            putInt(KEY_DIALOG_INPUT_BOTTOM_PADDING, settings.dialogInputBottomPadding)
            putInt(KEY_DIALOG_BUTTON_TOP_PADDING, settings.dialogButtonTopPadding)
            putInt(KEY_DIALOG_BUTTON_BOTTOM_PADDING, settings.dialogButtonBottomPadding)
            putInt(KEY_DIALOG_TITLE_FONT_SIZE, settings.dialogTitleFontSize)
            putInt(KEY_DIALOG_DIVIDER_HORIZONTAL_PADDING, settings.dialogDividerHorizontalPadding)

            // 天空蓝主题颜色配置
            // 主要颜色系统
            putString(KEY_SKY_BLUE_PRIMARY, settings.skyBluePrimary)
            putString(KEY_SKY_BLUE_ON_PRIMARY, settings.skyBlueOnPrimary)
            putString(KEY_SKY_BLUE_PRIMARY_CONTAINER, settings.skyBluePrimaryContainer)
            putString(KEY_SKY_BLUE_ON_PRIMARY_CONTAINER, settings.skyBlueOnPrimaryContainer)

            // 次要颜色系统
            putString(KEY_SKY_BLUE_SECONDARY, settings.skyBlueSecondary)
            putString(KEY_SKY_BLUE_ON_SECONDARY, settings.skyBlueOnSecondary)
            putString(KEY_SKY_BLUE_SECONDARY_CONTAINER, settings.skyBlueSecondaryContainer)
            putString(KEY_SKY_BLUE_ON_SECONDARY_CONTAINER, settings.skyBlueOnSecondaryContainer)

            // 第三颜色系统
            putString(KEY_SKY_BLUE_TERTIARY, settings.skyBlueTertiary)
            putString(KEY_SKY_BLUE_ON_TERTIARY, settings.skyBlueOnTertiary)
            putString(KEY_SKY_BLUE_TERTIARY_CONTAINER, settings.skyBlueTertiaryContainer)
            putString(KEY_SKY_BLUE_ON_TERTIARY_CONTAINER, settings.skyBlueOnTertiaryContainer)

            // 错误颜色系统
            putString(KEY_SKY_BLUE_ERROR, settings.skyBlueError)
            putString(KEY_SKY_BLUE_ON_ERROR, settings.skyBlueOnError)
            putString(KEY_SKY_BLUE_ERROR_CONTAINER, settings.skyBlueErrorContainer)
            putString(KEY_SKY_BLUE_ON_ERROR_CONTAINER, settings.skyBlueOnErrorContainer)

            // 表面颜色系统
            putString(KEY_SKY_BLUE_BACKGROUND, settings.skyBlueBackground)
            putString(KEY_SKY_BLUE_ON_BACKGROUND, settings.skyBlueOnBackground)
            putString(KEY_SKY_BLUE_SURFACE, settings.skyBlueSurface)
            putString(KEY_SKY_BLUE_ON_SURFACE, settings.skyBlueOnSurface)
            putString(KEY_SKY_BLUE_SURFACE_VARIANT, settings.skyBlueSurfaceVariant)
            putString(KEY_SKY_BLUE_ON_SURFACE_VARIANT, settings.skyBlueOnSurfaceVariant)

            // 扩展颜色
            putString(KEY_SKY_BLUE_CONFIRM, settings.skyBlueConfirm)
            putString(KEY_SKY_BLUE_FONT_EMPHASIZE, settings.skyBlueFontEmphasize)
            putString(KEY_SKY_BLUE_ICON_EMPHASIZE, settings.skyBlueIconEmphasize)
            putString(KEY_SKY_BLUE_ICON_SUB_EMPHASIZE, settings.skyBlueIconSubEmphasize)
            putString(KEY_SKY_BLUE_BACKGROUND_EMPHASIZE, settings.skyBlueBackgroundEmphasize)
            putString(KEY_SKY_BLUE_BACKGROUND_FOCUS, settings.skyBlueBackgroundFocus)

            // 底部导航栏颜色配置
            putString(KEY_SKY_BLUE_BOTTOM_NAV_BACKGROUND, settings.skyBlueBottomNavBackground)
            putString(KEY_SKY_BLUE_BOTTOM_NAV_SELECTED_ICON, settings.skyBlueBottomNavSelectedIcon)
            putString(KEY_SKY_BLUE_BOTTOM_NAV_UNSELECTED_ICON, settings.skyBlueBottomNavUnselectedIcon)

            // 标题栏颜色配置
            putString(KEY_SKY_BLUE_TOP_BAR_BACKGROUND, settings.skyBlueTopBarBackground)

            // 底部导航栏尺寸配置（天空蓝主题专用）
            putInt(KEY_GLOBAL_BOTTOM_NAV_HEIGHT, settings.bottomNavHeight)
            putInt(KEY_GLOBAL_BOTTOM_NAV_HORIZONTAL_PADDING, settings.bottomNavHorizontalPadding)
            putInt(KEY_GLOBAL_BOTTOM_NAV_VERTICAL_PADDING, settings.bottomNavVerticalPadding)
            putInt(KEY_GLOBAL_BOTTOM_NAV_ITEM_CORNER_RADIUS, settings.bottomNavItemCornerRadius)
            putInt(KEY_GLOBAL_BOTTOM_NAV_ITEM_OUTER_PADDING, settings.bottomNavItemOuterPadding)
            putInt(KEY_GLOBAL_BOTTOM_NAV_ITEM_VERTICAL_PADDING, settings.bottomNavItemVerticalPadding)
            putInt(KEY_GLOBAL_BOTTOM_NAV_ITEM_HORIZONTAL_PADDING, settings.bottomNavItemHorizontalPadding)
            putInt(KEY_GLOBAL_BOTTOM_NAV_ICON_SIZE, settings.bottomNavIconSize)
            putInt(KEY_GLOBAL_BOTTOM_NAV_ICON_TEXT_SPACING, settings.bottomNavIconTextSpacing)
            putInt(KEY_GLOBAL_BOTTOM_NAV_TEXT_FONT_SIZE, settings.bottomNavTextFontSize)
            putString(KEY_GLOBAL_BOTTOM_NAV_SELECTED_FONT_WEIGHT, settings.bottomNavSelectedFontWeight)
            putString(KEY_GLOBAL_BOTTOM_NAV_UNSELECTED_FONT_WEIGHT, settings.bottomNavUnselectedFontWeight)
            putInt(KEY_GLOBAL_BOTTOM_NAV_COLOR_ANIMATION_DURATION, settings.bottomNavColorAnimationDuration)
            putInt(KEY_GLOBAL_BOTTOM_NAV_BACKGROUND_ANIMATION_DURATION, settings.bottomNavBackgroundAnimationDuration)
            putString(KEY_GLOBAL_BOTTOM_NAV_ITEM_ARRANGEMENT, settings.bottomNavItemArrangement)

            // 对话框样式设置（天空蓝主题专用）
            putBoolean(KEY_GLOBAL_DIALOG_BLUR_ENABLED, settings.dialogBlurEnabled)
            putInt(KEY_GLOBAL_DIALOG_CORNER_RADIUS, settings.dialogCornerRadius)
            putFloat(KEY_GLOBAL_DIALOG_BLUR_INTENSITY, settings.dialogBlurIntensity)

            // 顶部应用栏按钮样式设置（天空蓝主题专用）
            putBoolean(KEY_GLOBAL_TOP_APP_BAR_BUTTON_CIRCLE_BACKGROUND_ENABLED, settings.topAppBarButtonCircleBackgroundEnabled)
            putInt(KEY_GLOBAL_TOP_APP_BAR_BUTTON_CIRCLE_BACKGROUND_SIZE, settings.topAppBarButtonCircleBackgroundSize)
            putInt(KEY_GLOBAL_TOP_APP_BAR_BUTTON_CIRCLE_BACKGROUND_HORIZONTAL_MARGIN, settings.topAppBarButtonCircleBackgroundHorizontalMargin)
            putInt(KEY_GLOBAL_TOP_APP_BAR_BUTTON_CIRCLE_BACKGROUND_RIGHT_MARGIN, settings.topAppBarButtonCircleBackgroundRightMargin)

            // 导航项显示控制设置（天空蓝主题专用）
            saveNavigationItemsVisibility(settings.navigationItemsVisibility)

            // 水球高级材质设置（天空蓝主题专用）
            putBoolean(KEY_GLOBAL_WATER_BALL_ADVANCED_MATERIAL_ENABLED, settings.waterBallAdvancedMaterialEnabled)
            putBoolean(KEY_GLOBAL_WATER_WAVE_RESERVE_SPACE_ENABLED, settings.waterWaveReserveSpaceEnabled)
            putInt(KEY_GLOBAL_WATER_WAVE_STOP_THRESHOLD, settings.waterWaveStopThreshold)

            // 悬浮加速球设置
            putBoolean(KEY_GLOBAL_FLOATING_ACCELERATOR_ENABLED, settings.floatingAcceleratorEnabled)

            // 保存网络状态（快捷指令的网络恢复功能需要）
            saveSavedNetworkState(settings.savedNetworkState)
        }.apply()

        _globalSettings.value = settings
        Log.d(TAG, "Saved global settings: $settings")
    }









    /**
     * 获取应用重要性映射
     */
    fun getAppImportanceMap(): Map<String, AppImportance> {
        val result = mutableMapOf<String, AppImportance>()
        val allPrefs = sharedPreferences.all

        for ((key, value) in allPrefs) {
            if (key.startsWith(PREFIX_APP_IMPORTANCE)) {
                val packageName = key.substring(PREFIX_APP_IMPORTANCE.length)
                val importanceOrdinal = value as? Int ?: continue
                try {
                    val importance = AppImportance.values()[importanceOrdinal]
                    result[packageName] = importance
                } catch (e: IndexOutOfBoundsException) {
                    Log.w(TAG, "Invalid importance ordinal for $packageName: $importanceOrdinal")
                }
            }
        }

        return result
    }

    /**
     * 获取单个应用的重要性
     */
    fun getAppImportance(packageName: String): AppImportance? {
        val importanceOrdinal = sharedPreferences.getInt("$PREFIX_APP_IMPORTANCE$packageName", -1)
        return if (importanceOrdinal >= 0 && importanceOrdinal < AppImportance.values().size) {
            AppImportance.values()[importanceOrdinal]
        } else {
            null
        }
    }

    /**
     * 设置单个应用的重要性
     */
    fun setAppImportance(packageName: String, importance: AppImportance?) {
        sharedPreferences.edit().apply {
            if (importance != null) {
                putInt("$PREFIX_APP_IMPORTANCE$packageName", importance.ordinal)
            } else {
                remove("$PREFIX_APP_IMPORTANCE$packageName")
            }
        }.apply()

        Log.d(TAG, "Set app importance for $packageName: $importance")
    }

    /**
     * 删除单个应用的重要性设置
     */
    fun removeAppImportance(packageName: String) {
        sharedPreferences.edit().apply {
            remove("$PREFIX_APP_IMPORTANCE$packageName")
        }.apply()

        Log.d(TAG, "Removed app importance for $packageName")
    }

    /**
     * 保存自定义清理策略 - 使用原生数据类型存储
     */
    fun saveCustomCleanupStrategy(strategy: CleanupStrategy) {
        try {
            val editor = sharedPreferences.edit()
            val prefix = "$PREFIX_CUSTOM_STRATEGY${strategy.id}_"

            // 保存策略基本信息
            editor.putString("${prefix}name", strategy.name)
            editor.putString("${prefix}description", strategy.description)
            editor.putBoolean("${prefix}is_preset", strategy.isPreset)

            // 保存规则数量
            editor.putInt("${prefix}rules_count", strategy.rules.size)

            // 保存每个规则
            strategy.rules.forEachIndexed { index, rule ->
                val rulePrefix = "${prefix}rule_${index}_"
                editor.putString("${rulePrefix}id", rule.id)
                editor.putString("${rulePrefix}type", rule.type.name)
                editor.putInt("${rulePrefix}order", rule.order)
                editor.putBoolean("${rulePrefix}enabled", rule.enabled)

                // 保存规则参数
                editor.putInt("${rulePrefix}parameters_count", rule.parameters.size)
                rule.parameters.entries.forEachIndexed { paramIndex, (key, value) ->
                    val paramPrefix = "${rulePrefix}param_${paramIndex}_"
                    editor.putString("${paramPrefix}key", key)
                    when (value) {
                        is String -> {
                            editor.putString("${paramPrefix}type", "string")
                            editor.putString("${paramPrefix}value", value)
                        }
                        is Int -> {
                            editor.putString("${paramPrefix}type", "int")
                            editor.putInt("${paramPrefix}value", value)
                        }
                        is Boolean -> {
                            editor.putString("${paramPrefix}type", "boolean")
                            editor.putBoolean("${paramPrefix}value", value)
                        }
                        is Float -> {
                            editor.putString("${paramPrefix}type", "float")
                            editor.putFloat("${paramPrefix}value", value)
                        }
                        is Long -> {
                            editor.putString("${paramPrefix}type", "long")
                            editor.putLong("${paramPrefix}value", value)
                        }
                        else -> {
                            editor.putString("${paramPrefix}type", "string")
                            editor.putString("${paramPrefix}value", value.toString())
                        }
                    }
                }
            }

            editor.apply()
            Log.d(TAG, "Saved custom cleanup strategy using native storage: ${strategy.name}")
        } catch (e: Exception) {
            Log.e(TAG, "Error saving custom cleanup strategy", e)
        }
    }

    /**
     * 获取自定义清理策略 - 使用原生数据类型存储
     */
    fun getCustomCleanupStrategy(strategyId: String): CleanupStrategy? {
        return try {
            val prefix = "$PREFIX_CUSTOM_STRATEGY${strategyId}_"

            // 检查策略是否存在
            if (!sharedPreferences.contains("${prefix}name")) {
                return null
            }

            // 加载策略基本信息
            val name = sharedPreferences.getString("${prefix}name", "") ?: ""
            val description = sharedPreferences.getString("${prefix}description", "") ?: ""
            val isPreset = sharedPreferences.getBoolean("${prefix}is_preset", false)

            // 加载规则
            val rulesCount = sharedPreferences.getInt("${prefix}rules_count", 0)
            val rules = mutableListOf<CleanupRule>()

            for (index in 0 until rulesCount) {
                val rulePrefix = "${prefix}rule_${index}_"

                val ruleId = sharedPreferences.getString("${rulePrefix}id", "") ?: ""
                val ruleTypeName = sharedPreferences.getString("${rulePrefix}type", "") ?: ""
                val ruleType = try {
                    CleanupRuleType.valueOf(ruleTypeName)
                } catch (e: Exception) {
                    Log.w(TAG, "Unknown rule type: $ruleTypeName, skipping rule")
                    continue
                }
                val order = sharedPreferences.getInt("${rulePrefix}order", 0)
                val enabled = sharedPreferences.getBoolean("${rulePrefix}enabled", true)

                // 加载规则参数
                val parametersCount = sharedPreferences.getInt("${rulePrefix}parameters_count", 0)
                val parameters = mutableMapOf<String, Any>()

                for (paramIndex in 0 until parametersCount) {
                    val paramPrefix = "${rulePrefix}param_${paramIndex}_"
                    val key = sharedPreferences.getString("${paramPrefix}key", "") ?: ""
                    val type = sharedPreferences.getString("${paramPrefix}type", "string") ?: "string"

                    val value: Any = when (type) {
                        "string" -> sharedPreferences.getString("${paramPrefix}value", "") ?: ""
                        "int" -> sharedPreferences.getInt("${paramPrefix}value", 0)
                        "boolean" -> sharedPreferences.getBoolean("${paramPrefix}value", false)
                        "float" -> sharedPreferences.getFloat("${paramPrefix}value", 0f)
                        "long" -> sharedPreferences.getLong("${paramPrefix}value", 0L)
                        else -> sharedPreferences.getString("${paramPrefix}value", "") ?: ""
                    }

                    if (key.isNotEmpty()) {
                        parameters[key] = value
                    }
                }

                val rule = CleanupRule(
                    id = ruleId,
                    type = ruleType,
                    order = order,
                    enabled = enabled,
                    parameters = parameters
                )
                rules.add(rule)
            }

            CleanupStrategy(
                id = strategyId,
                name = name,
                description = description,
                rules = rules.sortedBy { it.order },
                isPreset = isPreset
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error loading custom cleanup strategy", e)
            null
        }
    }

    /**
     * 获取所有自定义清理策略 - 使用原生数据类型存储
     */
    fun getAllCustomCleanupStrategies(): List<CleanupStrategy> {
        val strategies = mutableListOf<CleanupStrategy>()
        val allPrefs = sharedPreferences.all
        val strategyIds = mutableSetOf<String>()

        // 从所有键中提取策略ID
        for (key in allPrefs.keys) {
            if (key.startsWith(PREFIX_CUSTOM_STRATEGY) && key.endsWith("_name")) {
                val strategyId = key.removePrefix(PREFIX_CUSTOM_STRATEGY).removeSuffix("_name")
                strategyIds.add(strategyId)
            }
        }

        // 加载每个策略
        for (strategyId in strategyIds) {
            try {
                val strategy = getCustomCleanupStrategy(strategyId)
                if (strategy != null) {
                    strategies.add(strategy)
                }
            } catch (e: Exception) {
                Log.w(TAG, "Error loading custom strategy: $strategyId", e)
            }
        }

        return strategies.sortedBy { it.name }
    }

    /**
     * 删除自定义清理策略 - 使用原生数据类型存储
     */
    fun deleteCustomCleanupStrategy(strategyId: String) {
        try {
            val editor = sharedPreferences.edit()
            val allPrefs = sharedPreferences.all
            val prefix = "$PREFIX_CUSTOM_STRATEGY${strategyId}_"

            // 删除所有与该策略相关的键
            for (key in allPrefs.keys) {
                if (key.startsWith(prefix)) {
                    editor.remove(key)
                }
            }

            editor.apply()
            Log.d(TAG, "Deleted custom cleanup strategy using native storage: $strategyId")
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting custom cleanup strategy", e)
        }
    }

    // JSON序列化方法已移除，改用原生数据类型存储

    /**
     * 加载保存的网络状态 - 使用原生数据类型存储
     */
    private fun loadSavedNetworkState(): NetworkState? {
        return try {
            // 检查是否有保存的网络状态
            if (!sharedPreferences.contains("${KEY_GLOBAL_SAVED_NETWORK_STATE}_wifi_enabled")) {
                return null
            }

            val wifiEnabled = sharedPreferences.getBoolean("${KEY_GLOBAL_SAVED_NETWORK_STATE}_wifi_enabled", false)
            val mobileDataEnabled = sharedPreferences.getBoolean("${KEY_GLOBAL_SAVED_NETWORK_STATE}_mobile_data_enabled", false)
            val saveTime = sharedPreferences.getLong("${KEY_GLOBAL_SAVED_NETWORK_STATE}_save_time", 0L)

            NetworkState(
                wifiEnabled = wifiEnabled,
                mobileDataEnabled = mobileDataEnabled,
                saveTime = saveTime
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error loading saved network state", e)
            null
        }
    }

    /**
     * 保存网络状态 - 使用原生数据类型存储
     */
    private fun saveSavedNetworkState(networkState: NetworkState?) {
        try {
            val editor = sharedPreferences.edit()
            if (networkState != null) {
                editor.putBoolean("${KEY_GLOBAL_SAVED_NETWORK_STATE}_wifi_enabled", networkState.wifiEnabled)
                editor.putBoolean("${KEY_GLOBAL_SAVED_NETWORK_STATE}_mobile_data_enabled", networkState.mobileDataEnabled)
                editor.putLong("${KEY_GLOBAL_SAVED_NETWORK_STATE}_save_time", networkState.saveTime)
            } else {
                // 清除所有网络状态相关的键
                editor.remove("${KEY_GLOBAL_SAVED_NETWORK_STATE}_wifi_enabled")
                editor.remove("${KEY_GLOBAL_SAVED_NETWORK_STATE}_mobile_data_enabled")
                editor.remove("${KEY_GLOBAL_SAVED_NETWORK_STATE}_save_time")
            }
            editor.apply()
        } catch (e: Exception) {
            Log.e(TAG, "Error saving network state", e)
        }
    }



    /**
     * 清理过期的网络状态数据
     * 应在应用启动时调用
     */
    fun cleanupExpiredNetworkState() {
        try {
            val currentSettings = _globalSettings.value
            val savedNetworkState = currentSettings.savedNetworkState

            if (savedNetworkState != null && savedNetworkState.isExpired()) {
                Log.d(TAG, "Cleaning up expired network state")
                val updatedSettings = currentSettings.copy(savedNetworkState = null)
                saveGlobalSettings(updatedSettings)

                // 记录清理日志（使用Log而非Logger，避免协程依赖）
                Log.d(TAG, "Cleaned up expired network state: saveTime=${savedNetworkState.saveTime}, currentTime=${System.currentTimeMillis()}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error cleaning up expired network state", e)
        }
    }



    /**
     * 解锁实验性功能（仅解锁，不启用）
     */
    fun unlockExperimentalFeatures() {
        try {
            val currentSettings = _globalSettings.value
            val updatedSettings = currentSettings.copy(
                experimentalFeaturesUnlocked = true,
                experimentalFeaturesEnabled = false // 解锁后开关仍然关闭
            )
            saveGlobalSettings(updatedSettings)
            Log.d(TAG, "Experimental features unlocked (but not enabled)")
        } catch (e: Exception) {
            Log.e(TAG, "Error unlocking experimental features", e)
        }
    }

    /**
     * 启用实验性功能（临时状态）
     */
    fun enableExperimentalFeatures() {
        try {
            val currentSettings = _globalSettings.value
            val updatedSettings = currentSettings.copy(
                experimentalFeaturesEnabled = true
            )
            saveGlobalSettings(updatedSettings)
            Log.d(TAG, "Experimental features enabled")
        } catch (e: Exception) {
            Log.e(TAG, "Error enabling experimental features", e)
        }
    }

    /**
     * 重置实验性功能会话状态（应用启动时调用）
     * 重置临时状态和解锁状态，但保持已配置的实验性功能正常工作
     */
    fun resetExperimentalFeaturesSessionState() {
        try {
            val currentSettings = _globalSettings.value
            val updatedSettings = currentSettings.copy(
                experimentalFeaturesEnabled = false,
                experimentalFeaturesUnlocked = false
            )
            saveGlobalSettings(updatedSettings)
            Log.d(TAG, "Experimental features session state reset")
        } catch (e: Exception) {
            Log.e(TAG, "Error resetting experimental features session state", e)
        }
    }

    companion object {
        private const val TAG = "SettingsRepository"
        private const val PREFS_NAME = "background_manager_settings"

        // 保留快捷指令需要的设置键
        private const val KEY_GLOBAL_WIDGET_UPDATE_ENABLED = "global_widget_update_enabled"
        private const val KEY_GLOBAL_WIDGET_UPDATE_INTERVAL = "global_widget_update_interval"
        private const val KEY_GLOBAL_SAVED_NETWORK_STATE = "global_saved_network_state"
        private const val KEY_GLOBAL_EXPERIMENTAL_FEATURES_ENABLED = "global_experimental_features_enabled"
        private const val KEY_GLOBAL_EXPERIMENTAL_FEATURES_UNLOCKED = "global_experimental_features_unlocked"
        private const val KEY_GLOBAL_APP_LANGUAGE = "global_app_language"
        private const val KEY_GLOBAL_SEARCH_FIELD_PLACEHOLDER_FONT_WEIGHT = "global_search_field_placeholder_font_weight"
        private const val KEY_GLOBAL_SEARCH_FIELD_ICON_WEIGHT = "global_search_field_icon_weight"
        private const val KEY_GLOBAL_TOP_APP_BAR_HEIGHT = "global_top_app_bar_height"
        private const val KEY_GLOBAL_TOP_APP_BAR_TITLE_FONT_SIZE = "global_top_app_bar_title_font_size"
        private const val KEY_GLOBAL_TOP_APP_BAR_TITLE_FONT_WEIGHT = "global_top_app_bar_title_font_weight"
        private const val KEY_GLOBAL_TOP_APP_BAR_TITLE_VERTICAL_OFFSET = "global_top_app_bar_title_vertical_offset"
        private const val KEY_GLOBAL_TOP_APP_BAR_TITLE_HORIZONTAL_OFFSET = "global_top_app_bar_title_horizontal_offset"
        private const val KEY_GLOBAL_TOP_APP_BAR_TYPE = "global_top_app_bar_type"
        private const val KEY_GLOBAL_SCREEN_TITLE_FONT_SIZE = "global_screen_title_font_size"
        private const val KEY_GLOBAL_FORM_SECTION_TITLE_FONT_SIZE = "global_form_section_title_font_size"

        // 卡片样式设置键（天空蓝主题专用）
        private const val KEY_GLOBAL_CARD_CORNER_RADIUS = "global_card_corner_radius"
        private const val KEY_GLOBAL_CARD_DEFAULT_HORIZONTAL_PADDING = "global_card_default_horizontal_padding"
        private const val KEY_GLOBAL_CARD_DEFAULT_VERTICAL_PADDING = "global_card_default_vertical_padding"
        private const val KEY_GLOBAL_CARD_SETTINGS_VERTICAL_PADDING = "global_card_settings_vertical_padding"
        private const val KEY_GLOBAL_CARD_COMPACT_HORIZONTAL_PADDING = "global_card_compact_horizontal_padding"
        private const val KEY_GLOBAL_CARD_COMPACT_VERTICAL_PADDING = "global_card_compact_vertical_padding"
        private const val KEY_GLOBAL_CARD_LARGE_HORIZONTAL_PADDING = "global_card_large_horizontal_padding"
        private const val KEY_GLOBAL_CARD_LARGE_VERTICAL_PADDING = "global_card_large_vertical_padding"
        private const val KEY_GLOBAL_CARD_ITEM_SPACING = "global_card_item_spacing"
        private const val KEY_GLOBAL_CARD_SECTION_SPACING = "global_card_section_spacing"
        private const val KEY_GLOBAL_CARD_CONTENT_VERTICAL_SPACING = "global_card_content_vertical_spacing"
        private const val KEY_GLOBAL_CARD_CONTENT_HORIZONTAL_SPACING = "global_card_content_horizontal_spacing"
        private const val KEY_GLOBAL_CARD_SELECTED_ELEVATION = "global_card_selected_elevation"
        private const val KEY_GLOBAL_CARD_SELECTED_BORDER_WIDTH = "global_card_selected_border_width"
        private const val KEY_GLOBAL_CARD_TITLE_FONT_SIZE = "global_card_title_font_size"
        private const val KEY_GLOBAL_CARD_TITLE_FONT_WEIGHT = "global_card_title_font_weight"
        private const val KEY_GLOBAL_CARD_CONTENT_FONT_SIZE = "global_card_content_font_size"
        private const val KEY_GLOBAL_CARD_CONTENT_FONT_WEIGHT = "global_card_content_font_weight"
        private const val KEY_GLOBAL_CARD_ICON_SIZE = "global_card_icon_size"

        // 页面布局设置键（天空蓝主题专用）
        private const val KEY_GLOBAL_PAGE_CONTENT_HORIZONTAL_PADDING = "global_page_content_horizontal_padding"
        private const val KEY_GLOBAL_PAGE_SEARCH_FIELD_MARGIN = "global_page_search_field_margin"
        private const val KEY_GLOBAL_PAGE_HEADER_SPACING = "global_page_header_spacing"
        private const val KEY_GLOBAL_PAGE_BOTTOM_PADDING = "global_page_bottom_padding"
        private const val KEY_GLOBAL_PAGE_SCROLL_CONTENT_SPACING = "global_page_scroll_content_spacing"

        // UI间距设置键（天空蓝主题专用）
        private const val KEY_UI_SETTINGS_ITEM_VERTICAL_PADDING = "ui_settings_item_vertical_padding"
        private const val KEY_UI_DIVIDER_HORIZONTAL_PADDING = "ui_divider_horizontal_padding"
        private const val KEY_UI_SETTINGS_CARD_PADDING = "ui_settings_card_padding"
        private const val KEY_UI_SETTINGS_ITEM_SPACING = "ui_settings_item_spacing"
        private const val KEY_UI_SETTINGS_TITLE_SPACING = "ui_settings_title_spacing"
        private const val KEY_UI_SETTINGS_DESCRIPTION_SPACING = "ui_settings_description_spacing"
        private const val KEY_UI_SETTINGS_GROUP_TITLE_HORIZONTAL_PADDING = "ui_settings_group_title_horizontal_padding"
        private const val KEY_UI_SETTINGS_GROUP_TITLE_TOP_PADDING = "ui_settings_group_title_top_padding"
        private const val KEY_UI_SETTINGS_GROUP_TITLE_BOTTOM_PADDING = "ui_settings_group_title_bottom_padding"
        private const val KEY_UI_DIVIDER_VISIBLE = "ui_divider_visible"

        // 用户体验设置键
        private const val KEY_CHECKUP_RESULT_DELAY_ENABLED = "checkup_result_delay_enabled"
        private const val KEY_CHECKUP_DISPLAY_DURATION_SECONDS = "checkup_display_duration_seconds"
        private const val KEY_FIXED_CHECKUP_SCORE_ENABLED = "fixed_checkup_score_enabled"
        private const val KEY_FIXED_CHECKUP_SCORE = "fixed_checkup_score"

        // 对话框间距设置键（天空蓝主题专用）
        private const val KEY_DIALOG_OUTER_PADDING = "dialog_outer_padding"
        private const val KEY_DIALOG_ICON_BOTTOM_PADDING = "dialog_icon_bottom_padding"
        private const val KEY_DIALOG_TITLE_BOTTOM_PADDING = "dialog_title_bottom_padding"
        private const val KEY_DIALOG_CONTENT_BOTTOM_PADDING = "dialog_content_bottom_padding"
        private const val KEY_DIALOG_CONTENT_VERTICAL_PADDING = "dialog_content_vertical_padding"
        private const val KEY_DIALOG_INPUT_BOTTOM_PADDING = "dialog_input_bottom_padding"
        private const val KEY_DIALOG_BUTTON_TOP_PADDING = "dialog_button_top_padding"
        private const val KEY_DIALOG_BUTTON_BOTTOM_PADDING = "dialog_button_bottom_padding"
        private const val KEY_DIALOG_TITLE_FONT_SIZE = "dialog_title_font_size"
        private const val KEY_DIALOG_DIVIDER_HORIZONTAL_PADDING = "dialog_divider_horizontal_padding"

        // 天空蓝主题颜色配置键
        // 主要颜色系统
        private const val KEY_SKY_BLUE_PRIMARY = "sky_blue_primary"
        private const val KEY_SKY_BLUE_ON_PRIMARY = "sky_blue_on_primary"
        private const val KEY_SKY_BLUE_PRIMARY_CONTAINER = "sky_blue_primary_container"
        private const val KEY_SKY_BLUE_ON_PRIMARY_CONTAINER = "sky_blue_on_primary_container"

        // 次要颜色系统
        private const val KEY_SKY_BLUE_SECONDARY = "sky_blue_secondary"
        private const val KEY_SKY_BLUE_ON_SECONDARY = "sky_blue_on_secondary"
        private const val KEY_SKY_BLUE_SECONDARY_CONTAINER = "sky_blue_secondary_container"
        private const val KEY_SKY_BLUE_ON_SECONDARY_CONTAINER = "sky_blue_on_secondary_container"

        // 第三颜色系统
        private const val KEY_SKY_BLUE_TERTIARY = "sky_blue_tertiary"
        private const val KEY_SKY_BLUE_ON_TERTIARY = "sky_blue_on_tertiary"
        private const val KEY_SKY_BLUE_TERTIARY_CONTAINER = "sky_blue_tertiary_container"
        private const val KEY_SKY_BLUE_ON_TERTIARY_CONTAINER = "sky_blue_on_tertiary_container"

        // 错误颜色系统
        private const val KEY_SKY_BLUE_ERROR = "sky_blue_error"
        private const val KEY_SKY_BLUE_ON_ERROR = "sky_blue_on_error"
        private const val KEY_SKY_BLUE_ERROR_CONTAINER = "sky_blue_error_container"
        private const val KEY_SKY_BLUE_ON_ERROR_CONTAINER = "sky_blue_on_error_container"

        // 表面颜色系统
        private const val KEY_SKY_BLUE_BACKGROUND = "sky_blue_background"
        private const val KEY_SKY_BLUE_ON_BACKGROUND = "sky_blue_on_background"
        private const val KEY_SKY_BLUE_SURFACE = "sky_blue_surface"
        private const val KEY_SKY_BLUE_ON_SURFACE = "sky_blue_on_surface"
        private const val KEY_SKY_BLUE_SURFACE_VARIANT = "sky_blue_surface_variant"
        private const val KEY_SKY_BLUE_ON_SURFACE_VARIANT = "sky_blue_on_surface_variant"

        // 扩展颜色
        private const val KEY_SKY_BLUE_CONFIRM = "sky_blue_confirm"
        private const val KEY_SKY_BLUE_FONT_EMPHASIZE = "sky_blue_font_emphasize"
        private const val KEY_SKY_BLUE_ICON_EMPHASIZE = "sky_blue_icon_emphasize"
        private const val KEY_SKY_BLUE_ICON_SUB_EMPHASIZE = "sky_blue_icon_sub_emphasize"
        private const val KEY_SKY_BLUE_BACKGROUND_EMPHASIZE = "sky_blue_background_emphasize"
        private const val KEY_SKY_BLUE_BACKGROUND_FOCUS = "sky_blue_background_focus"

        // 底部导航栏颜色配置
        private const val KEY_SKY_BLUE_BOTTOM_NAV_BACKGROUND = "sky_blue_bottom_nav_background"
        private const val KEY_SKY_BLUE_BOTTOM_NAV_SELECTED_ICON = "sky_blue_bottom_nav_selected_icon"
        private const val KEY_SKY_BLUE_BOTTOM_NAV_UNSELECTED_ICON = "sky_blue_bottom_nav_unselected_icon"

        // 标题栏颜色配置
        private const val KEY_SKY_BLUE_TOP_BAR_BACKGROUND = "sky_blue_top_bar_background"

        // 底部导航栏尺寸配置键（天空蓝主题专用）
        private const val KEY_GLOBAL_BOTTOM_NAV_HEIGHT = "global_bottom_nav_height"
        private const val KEY_GLOBAL_BOTTOM_NAV_HORIZONTAL_PADDING = "global_bottom_nav_horizontal_padding"
        private const val KEY_GLOBAL_BOTTOM_NAV_VERTICAL_PADDING = "global_bottom_nav_vertical_padding"
        private const val KEY_GLOBAL_BOTTOM_NAV_ITEM_CORNER_RADIUS = "global_bottom_nav_item_corner_radius"
        private const val KEY_GLOBAL_BOTTOM_NAV_ITEM_OUTER_PADDING = "global_bottom_nav_item_outer_padding"
        private const val KEY_GLOBAL_BOTTOM_NAV_ITEM_VERTICAL_PADDING = "global_bottom_nav_item_vertical_padding"
        private const val KEY_GLOBAL_BOTTOM_NAV_ITEM_HORIZONTAL_PADDING = "global_bottom_nav_item_horizontal_padding"
        private const val KEY_GLOBAL_BOTTOM_NAV_ICON_SIZE = "global_bottom_nav_icon_size"
        private const val KEY_GLOBAL_BOTTOM_NAV_ICON_TEXT_SPACING = "global_bottom_nav_icon_text_spacing"
        private const val KEY_GLOBAL_BOTTOM_NAV_TEXT_FONT_SIZE = "global_bottom_nav_text_font_size"
        private const val KEY_GLOBAL_BOTTOM_NAV_SELECTED_FONT_WEIGHT = "global_bottom_nav_selected_font_weight"
        private const val KEY_GLOBAL_BOTTOM_NAV_UNSELECTED_FONT_WEIGHT = "global_bottom_nav_unselected_font_weight"
        private const val KEY_GLOBAL_BOTTOM_NAV_COLOR_ANIMATION_DURATION = "global_bottom_nav_color_animation_duration"
        private const val KEY_GLOBAL_BOTTOM_NAV_BACKGROUND_ANIMATION_DURATION = "global_bottom_nav_background_animation_duration"
        private const val KEY_GLOBAL_BOTTOM_NAV_ITEM_ARRANGEMENT = "global_bottom_nav_item_arrangement"

        // 对话框样式设置键（天空蓝主题专用）
        private const val KEY_GLOBAL_DIALOG_BLUR_ENABLED = "global_dialog_blur_enabled"
        private const val KEY_GLOBAL_DIALOG_CORNER_RADIUS = "global_dialog_corner_radius"
        private const val KEY_GLOBAL_DIALOG_BLUR_INTENSITY = "global_dialog_blur_intensity"

        // 顶部应用栏按钮样式设置键（天空蓝主题专用）
        private const val KEY_GLOBAL_TOP_APP_BAR_BUTTON_CIRCLE_BACKGROUND_ENABLED = "global_top_app_bar_button_circle_background_enabled"
        private const val KEY_GLOBAL_TOP_APP_BAR_BUTTON_CIRCLE_BACKGROUND_SIZE = "global_top_app_bar_button_circle_background_size"
        private const val KEY_GLOBAL_TOP_APP_BAR_BUTTON_CIRCLE_BACKGROUND_HORIZONTAL_MARGIN = "global_top_app_bar_button_circle_background_horizontal_margin"
        private const val KEY_GLOBAL_TOP_APP_BAR_BUTTON_CIRCLE_BACKGROUND_RIGHT_MARGIN = "global_top_app_bar_button_circle_background_right_margin"

        // 水球高级材质设置键（天空蓝主题专用）
        private const val KEY_GLOBAL_WATER_BALL_ADVANCED_MATERIAL_ENABLED = "global_water_ball_advanced_material_enabled"
        private const val KEY_GLOBAL_WATER_WAVE_RESERVE_SPACE_ENABLED = "global_water_wave_reserve_space_enabled"
        private const val KEY_GLOBAL_WATER_WAVE_STOP_THRESHOLD = "global_water_wave_stop_threshold"

        // 悬浮加速球设置键
        private const val KEY_GLOBAL_FLOATING_ACCELERATOR_ENABLED = "global_floating_accelerator_enabled"

        // 导航项显示控制设置键（天空蓝主题专用）
        private const val KEY_NAVIGATION_ITEM_VISIBILITY_PREFIX = "navigation_item_visibility_"

        // 保留快捷指令需要的前缀
        private const val PREFIX_APP_IMPORTANCE = "app_importance_"
        private const val PREFIX_CUSTOM_STRATEGY = "custom_strategy_"
    }

    /**
     * 加载导航项显示状态
     */
    private fun loadNavigationItemsVisibility(): Map<String, Boolean> {
        val defaultVisibility = mapOf(
            "phone_checkup" to true,
            "quick_commands" to true,
            "command_templates" to true,
            "smart_reminders" to true,
            "global_settings" to true
        )

        return defaultVisibility.mapValues { (key, defaultValue) ->
            sharedPreferences.getBoolean("$KEY_NAVIGATION_ITEM_VISIBILITY_PREFIX$key", defaultValue)
        }
    }

    /**
     * 保存导航项显示状态
     */
    private fun saveNavigationItemsVisibility(visibility: Map<String, Boolean>) {
        val editor = sharedPreferences.edit()
        visibility.forEach { (key, value) ->
            editor.putBoolean("$KEY_NAVIGATION_ITEM_VISIBILITY_PREFIX$key", value)
        }
        editor.apply()
    }
}
